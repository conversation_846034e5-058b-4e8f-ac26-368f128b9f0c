# 媒体预览和删除功能测试指南

## 功能概述

本次更新实现了以下功能：

1. **MediaPreviewModal 删除功能**
   - 在预览组件中添加了删除按钮（仅在 `isDelete=true` 时显示）
   - 实现了 `deleteMedia` 方法，支持删除图片和视频
   - 删除时会确认操作，并同步更新父组件数据

2. **文件上传数量限制**
   - 图片最多可以上传 9 个
   - 视频最多只能上传 1 个
   - 超出限制时会显示相应的错误提示

3. **数据同步**
   - 删除操作会同步更新表格记录中的数据
   - 预览组件的数据会实时更新

## 测试步骤

### 1. 测试文件上传限制

#### 图片上传限制测试：
1. 打开评价详情页面，进入编辑模式
2. 选择一个支持图片上传的评分项
3. 连续上传图片，观察是否在第10个图片时显示错误提示："图片最多只能上传9个"

#### 视频上传限制测试：
1. 选择一个支持视频上传的评分项
2. 上传第一个视频，应该成功
3. 尝试上传第二个视频，应该显示错误提示："视频最多只能上传1个"

### 2. 测试删除功能

#### 在编辑模式下测试删除：
1. 上传一些图片和视频文件
2. 点击缩略图打开预览组件
3. 确认删除按钮显示（红色的关闭图标）
4. 点击删除按钮，确认删除操作
5. 观察：
   - 文件从预览中消失
   - 如果删除所有文件，预览组件应该关闭
   - 返回表格，确认缩略图也相应更新

#### 在查看模式下测试：
1. 在非编辑模式下打开预览组件
2. 确认删除按钮不显示（`isDelete=false`）

### 3. 测试数据同步

1. 在编辑模式下上传多个文件
2. 打开预览，删除部分文件
3. 关闭预览，观察表格中的缩略图是否正确更新
4. 重新打开预览，确认显示的文件列表正确

## 代码变更说明

### MediaPreviewModal.vue 变更：
- 添加了 `CloseCircleOutlined` 图标导入
- 新增 `delete-media` 事件发射
- 实现了完整的 `deleteMedia` 方法
- 删除按钮仅在 `props.isDelete` 为 true 时显示

### evaluationDetailed.vue 变更：
- 在预览组件上添加了 `@delete-media="handleDeleteMedia"` 事件监听
- 新增 `handleDeleteMedia` 方法处理删除逻辑
- 在 `previewModal` 状态中添加了 `currentRecord` 字段
- 在 `handleFileUpload` 方法中添加了数量限制检查

## 注意事项

1. 删除操作会立即生效，无法撤销
2. 文件上传限制是前端验证，建议后端也添加相应验证
3. 删除功能仅在编辑模式下可用
4. 数量限制基于当前已上传的文件数量进行检查
