# 文件上传多选功能修改总结

## 修改内容

### 1. 启用多选文件上传
- 在 `a-upload` 组件中添加了 `multiple` 属性，支持多选文件
- 修改了 `before-upload` 处理函数，从 `handleFileUpload` 改为 `handleSingleFileUpload`

### 2. 重写文件上传处理逻辑
- 创建了新的 `handleSingleFileUpload` 函数来处理单文件上传（每个文件单独调用）
- 修复了数量限制逻辑，确保每行最多只能上传9个图片和1个视频
- 保持原有的文件类型验证逻辑
- 添加了实时的数量检查和错误提示

### 3. 保持缩略图显示逻辑不变
- 恢复了原来的缩略图显示逻辑，只显示第一个文件
- 保持多文件指示器（+N）显示
- 保持原有的预览和删除功能

### 4. 优化用户界面
- 更新上传按钮文字，添加"批量"前缀
- 保持原有的缩略图尺寸和布局（100x100px）
- 保持与 MediaPreviewModal 组件的完全兼容

### 5. 功能特性
- **多文件选择**: 用户可以一次选择多个文件进行上传
- **类型验证**: 仍然支持图片和视频文件类型验证
- **严格数量限制**: 每行最多9个图片和1个视频，超出限制会立即提示并阻止上传
- **单文件上传**: 每个文件单独上传，避免批量上传的复杂性
- **实时反馈**: 每个文件上传成功后立即显示提示
- **错误处理**: 文件类型错误或数量超限时立即提示

## 主要修改的文件

### `src/pages/yd-evaluation/evaluationManage/components/evaluationDetailed.vue`

#### 模板修改:
```vue
<!-- 添加 multiple 属性 -->
<a-upload
    :file-list="[]"
    :before-upload="file => handleSingleFileUpload(file, record)"
    :accept="getAcceptTypes(record.evalScoreTypeList)"
    :show-upload-list="false"
    multiple
>
    <a-button size="small" type="primary" ghost>
        <template #icon>
            <CloudUploadOutlined />
        </template>
        {{ getUploadButtonText(record.evalScoreTypeList) }}
    </a-button>
</a-upload>

<!-- 保持原有缩略图显示逻辑 -->
<div class="thumbnail-container" style="margin-top: 8px">
    <div
        v-if="getFirstMediaFile(record)"
        class="thumbnail-item"
        @click="openPreviewModal(record)"
    >
        <!-- 图片缩略图 -->
        <img
            v-if="getFirstMediaFile(record).type === 'image'"
            :src="getFirstMediaFile(record).url"
            alt="缩略图"
        />
        <!-- 视频缩略图 -->
        <video
            v-else-if="getFirstMediaFile(record).type === 'video'"
            :src="getFirstMediaFile(record).url"
            muted
        ></video>

        <div class="thumbnail-overlay">
            <EyeOutlined v-if="getFirstMediaFile(record).type === 'image'" />
            <PlayCircleOutlined v-else />
        </div>

        <!-- 多文件指示器 -->
        <div v-if="getTotalMediaCount(record) > 1" class="media-count-badge">
            +{{ getTotalMediaCount(record) - 1 }}
        </div>
    </div>
</div>
```

#### 脚本修改:
```javascript
// 单文件上传处理（每个文件会单独调用此函数）
const handleSingleFileUpload = (file, record) => {
    // 文件类型验证
    const isImage = file.type.startsWith('image/')
    const isVideo = file.type.startsWith('video/')

    // 检查是否允许上传该类型文件
    const allowImage = record.evalScoreTypeList.includes('image')
    const allowVideo = record.evalScoreTypeList.includes('video')

    // 获取当前已有的文件数量
    const currentImageCount = record.imgPaths ? getImageList(record.imgPaths).length : 0
    const currentVideoCount = record.videoPaths ? getVideoList(record.videoPaths).length : 0

    // 严格的数量限制检查
    if (isImage && currentImageCount >= 9) {
        YMessage.error('图片最多只能上传9个')
        return false
    }

    if (isVideo && currentVideoCount >= 1) {
        YMessage.error('视频最多只能上传1个')
        return false
    }

    // 上传文件并更新数据
    // ...

    return false // 阻止默认上传行为
}
```

#### 样式修改:
- 保持原有的 `.thumbnail-list` 布局
- 保持缩略图尺寸为 100x100px
- 保持多文件指示器样式

## 使用说明

1. **选择文件**: 点击上传按钮，可以同时选择多个图片或视频文件
2. **类型限制**: 根据评分方式配置，只能上传允许的文件类型
3. **数量限制**: 图片总数不超过9个，视频总数不超过1个
4. **预览功能**: 点击任意缩略图可以打开预览模态框查看所有文件
5. **删除功能**: 在预览模态框中可以删除不需要的文件

## 兼容性

- 保持与现有 MediaPreviewModal 组件的完全兼容
- 保持原有的数据结构和API接口不变
- 向后兼容单文件上传的使用场景
