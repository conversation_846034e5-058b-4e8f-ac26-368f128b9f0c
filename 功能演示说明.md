# 媒体预览删除功能演示

## 🎯 功能概述

本次开发完成了以下核心功能：

### 1. 媒体预览组件删除功能
- ✅ 在 `MediaPreviewModal.vue` 中添加了删除按钮
- ✅ 只有在 `isDelete=true` 时才显示删除按钮
- ✅ 点击删除按钮会弹出确认对话框
- ✅ 删除后会同步更新父组件的数据

### 2. 文件上传数量限制
- ✅ 图片最多上传 9 个
- ✅ 视频最多上传 1 个  
- ✅ 超出限制时显示友好的错误提示

### 3. 数据同步机制
- ✅ 删除操作实时同步到表格数据
- ✅ 预览组件数据自动更新
- ✅ 缩略图显示正确更新

## 🔧 技术实现要点

### MediaPreviewModal.vue 关键变更：

```vue
<!-- 删除按钮只在编辑模式下显示 -->
<div v-if="props.isDelete" class="del_icon" @click="deleteMedia(media, $event)">
    <CloseCircleOutlined style="color: red; font-size: 18px" />
</div>
```

```javascript
// 删除媒体文件方法
const deleteMedia = async (media, event) => {
    // 阻止事件冒泡
    if (event) {
        event.stopPropagation()
    }

    // 确认删除
    const confirmed = await yConfirm('确认删除', '确定要删除这个文件吗？')
    if (!confirmed) return

    // 发送删除事件给父组件
    emit('delete-media', {
        type: media.type,
        url: media.url
    })

    // 处理索引调整逻辑...
}
```

### evaluationDetailed.vue 关键变更：

```javascript
// 文件上传数量限制
const handleFileUpload = (file, record) => {
    // 图片数量检查
    if (isImage) {
        const currentImageCount = record.imgPaths ? getImageList(record.imgPaths).length : 0
        if (currentImageCount >= 9) {
            YMessage.error('图片最多只能上传9个')
            return false
        }
    }

    // 视频数量检查
    if (isVideo) {
        const currentVideoCount = record.videoPaths ? getVideoList(record.videoPaths).length : 0
        if (currentVideoCount >= 1) {
            YMessage.error('视频最多只能上传1个')
            return false
        }
    }
}

// 处理删除媒体事件
const handleDeleteMedia = ({ type, url }) => {
    const record = previewModal.currentRecord
    if (!record) return

    if (type === 'image') {
        // 从图片路径中删除指定URL
        const imageList = record.imgPaths.split(',').filter(path => path.trim())
        const updatedList = imageList.filter(path => path.trim() !== url)
        record.imgPaths = updatedList.join(',')
        previewModal.imgPaths = record.imgPaths
    } else if (type === 'video') {
        // 从视频路径中删除指定URL
        const videoList = record.videoPaths.split(',').filter(path => path.trim())
        const updatedList = videoList.filter(path => path.trim() !== url)
        record.videoPaths = updatedList.join(',')
        previewModal.videoPaths = record.videoPaths
    }
}
```

## 🎮 使用方式

### 在编辑模式下：
1. 点击缩略图打开预览组件
2. 在预览组件中可以看到红色的删除按钮
3. 点击删除按钮确认删除
4. 文件被删除，数据同步更新

### 文件上传限制：
1. 尝试上传第10张图片时会提示："图片最多只能上传9个"
2. 尝试上传第2个视频时会提示："视频最多只能上传1个"

## 🔍 测试建议

1. **删除功能测试**：
   - 上传多个文件后测试删除
   - 删除最后一个文件时预览组件应该关闭
   - 删除中间文件时索引应该正确调整

2. **数量限制测试**：
   - 测试图片上传限制（9个）
   - 测试视频上传限制（1个）
   - 测试混合上传场景

3. **数据同步测试**：
   - 删除后检查表格数据是否正确更新
   - 重新打开预览检查数据一致性

## ⚠️ 注意事项

- 删除操作不可撤销，请谨慎操作
- 建议在后端也添加相应的数量限制验证
- 删除功能仅在编辑模式（`isEdit=true`）下可用
